# Go API Solve - API接入指南

## 📋 服务概述

Go API Solve 是一个基于Go语言开发的智能图片题目解析服务，集成了Qwen-VL-Plus和DeepSeek-Chat AI模型，提供高效的图片识别和题目解析功能。

### 🎯 核心功能
- **图片题目识别**: 支持单选题、多选题、判断题的自动识别
- **智能解析**: 提供详细的题目解析和答案
- **多级缓存**: Redis + MySQL双重缓存，提升响应速度
- **高可用性**: 完善的错误处理和容错机制

### 🏗️ 技术架构
- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **AI模型**: Qwen-VL-Plus + DeepSeek-Chat

---

## 🚀 快速接入

### 1. 服务地址
- **生产环境**: `http://your-domain.com:8080`
- **测试环境**: `http://localhost:8080`

### 2. 基础信息
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: POST

---

## 📡 API接口详情

### 1. 图片题目处理接口

#### 接口信息
- **接口地址**: `POST /api/v1/process-image`
- **功能描述**: 处理图片中的题目，返回识别结果和解析
- **请求超时**: 60秒

#### 请求参数

**请求头 (Headers)**
```http
Content-Type: application/json
Accept: application/json
```

**请求体 (Request Body)**
```json
{
  "image_url": "http://solve.igmdns.com/img/24.jpg"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image_url | string | 是 | 图片的完整URL地址，支持HTTP/HTTPS协议 |

#### 响应格式

**成功响应 (HTTP 200)**
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "多选题",
      "question_text": "雾天跟车行驶,应如何安全驾驶?",
      "options": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物",
        "D": "按喇叭提示行车位置"
      },
      "answer": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物"
      },
      "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度，以确保有足够的反应时间。其次要提前开启雾灯和危险报警闪光灯，提高车辆的可见性。同时可以以前车尾灯作为判断安全距离的参照物。按喇叭虽然可以提示位置，但在雾天效果有限，不是最主要的安全措施。",
      "image_url": "http://solve.igmdns.com/img/24.jpg",
      "user_image": "24.jpg",
      "is_verified": "0"
    }
  ]
}
```

**响应字段说明**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应状态码，200表示成功 |
| message | string | 响应消息 |
| data | array | 题目数据数组（支持多题目） |

**题目数据字段 (data数组中的对象)**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| question_type | string | 题目类型：单选题/多选题/判断题 |
| question_text | string | 题目内容（已清洗格式化） |
| options | object | 选项内容，键值对格式 |
| answer | object | 正确答案，键值对格式 |
| analysis | string | 题目解析说明 |
| image_url | string | 原始图片URL |
| user_image | string | 图片文件名 |
| is_verified | string | 是否已验证：0-未验证，1-已验证 |

#### 不同题目类型的响应示例

**单选题示例**
```json
{
  "question_type": "单选题",
  "question_text": "驾驶机动车在高速公路上行驶，遇到雾天时应该？",
  "options": {
    "A": "开启远光灯",
    "B": "开启雾灯",
    "C": "开启双闪",
    "D": "正常行驶"
  },
  "answer": {
    "B": "开启雾灯"
  },
  "analysis": "雾天行驶应开启雾灯以提高可见性，远光灯会造成反射影响视线。"
}
```

**判断题示例**
```json
{
  "question_type": "判断题",
  "question_text": "驾驶机动车在雾天应使用远光灯提高能见度。",
  "options": {
    "Y": "正确",
    "N": "错误"
  },
  "answer": {
    "N": "错误"
  },
  "analysis": "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯。"
}
```

#### 错误响应

**参数错误 (HTTP 400)**
```json
{
  "code": 400,
  "message": "请求参数错误: Key: 'ProcessImageRequest.ImageURL' Error:Field validation for 'ImageURL' failed on the 'required' tag"
}
```

**图片资源错误 (HTTP 400)**
```json
{
  "code": 400,
  "message": "图片资源不存在，请重新上传"
}
```

**图片解析异常 (HTTP 400)**
```json
{
  "code": 400,
  "message": "图片解析异常，请重新拍摄"
}
```

**服务器错误 (HTTP 500)**
```json
{
  "code": 500,
  "message": "服务器内部错误: 具体错误信息"
}
```

### 2. 健康检查接口

#### 接口信息
- **接口地址**: `GET /api/v1/health`
- **功能描述**: 检查服务运行状态

#### 响应示例
```json
{
  "code": 200,
  "message": "服务正常运行",
  "data": {
    "status": "healthy",
    "service": "go-api-solve"
  }
}
```

---

## 🔧 接入示例

### cURL示例
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://solve.igmdns.com/img/24.jpg"
  }'
```

### JavaScript示例
```javascript
const processImage = async (imageUrl) => {
  try {
    const response = await fetch('http://localhost:8080/api/v1/process-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('处理成功:', result.data);
      return result.data;
    } else {
      console.error('处理失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
};

// 使用示例
processImage('http://solve.igmdns.com/img/24.jpg');
```

### Python示例
```python
import requests
import json

def process_image(image_url):
    """处理图片题目"""
    url = "http://localhost:8080/api/v1/process-image"
    
    payload = {
        "image_url": image_url
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        result = response.json()
        
        if result.get("code") == 200:
            print("处理成功")
            return result.get("data")
        else:
            print(f"处理失败: {result.get('message')}")
            return None
            
    except requests.exceptions.Timeout:
        print("请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None

# 使用示例
data = process_image("http://solve.igmdns.com/img/24.jpg")
if data:
    for question in data:
        print(f"题目类型: {question['question_type']}")
        print(f"题目内容: {question['question_text']}")
        print(f"选项: {question['options']}")
        print(f"答案: {question['answer']}")
        print(f"解析: {question['analysis']}")
```

### Java示例
```java
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.time.Duration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

public class ApiClient {
    private static final String API_URL = "http://localhost:8080/api/v1/process-image";
    private static final HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    
    public static void processImage(String imageUrl) {
        try {
            String requestBody = String.format("{\"image_url\":\"%s\"}", imageUrl);
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(API_URL))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .timeout(Duration.ofSeconds(60))
                    .build();
            
            HttpResponse<String> response = client.send(request, 
                    HttpResponse.BodyHandlers.ofString());
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response.body());
            
            if (jsonNode.get("code").asInt() == 200) {
                System.out.println("处理成功: " + jsonNode.get("data"));
            } else {
                System.out.println("处理失败: " + jsonNode.get("message"));
            }
            
        } catch (Exception e) {
            System.err.println("请求错误: " + e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        processImage("http://solve.igmdns.com/img/24.jpg");
    }
}
```

---

## ⚠️ 重要注意事项

### 1. 图片要求
- **格式支持**: JPG, JPEG, PNG, GIF, BMP, WEBP
- **大小限制**: 建议不超过10MB
- **分辨率**: 建议最小800x600，最大4096x4096
- **内容要求**: 图片应包含清晰的题目文字和选项

### 2. URL要求
- 必须是完整的HTTP/HTTPS URL
- 图片资源必须可公开访问
- 建议使用HTTPS协议确保安全性
- URL长度不超过1000字符

### 3. 性能考虑
- **响应时间**: 通常5-30秒，复杂图片可能需要更长时间
- **并发限制**: 建议单个客户端并发请求不超过10个
- **缓存机制**: 相同图片内容会使用缓存，响应更快

### 4. 错误处理
- 请根据HTTP状态码和响应中的code字段判断请求结果
- 建议实现重试机制，但避免频繁重试
- 对于400错误，检查请求参数；对于500错误，可以重试

### 5. 安全建议
- 在生产环境中使用HTTPS
- 对敏感图片URL进行适当的访问控制
- 建议实现请求频率限制

---

## 🔍 状态码说明

| HTTP状态码 | API Code | 说明 | 处理建议 |
|------------|----------|------|----------|
| 200 | 200 | 请求成功 | 正常处理返回数据 |
| 400 | 400 | 请求参数错误 | 检查请求参数格式 |
| 400 | 400 | 图片资源不存在 | 检查图片URL有效性 |
| 400 | 400 | 图片解析异常 | 重新拍摄或更换图片 |
| 500 | 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

---

## 📞 技术支持

如果您在接入过程中遇到问题，请联系我们的技术支持团队：

- **技术文档**: 查看项目README.md获取更多详细信息
- **问题反馈**: 通过项目Issues提交问题
- **部署指南**: 参考docs/DEPLOYMENT.md了解部署详情

---

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 支持单选题、多选题、判断题识别
- ✅ 集成Qwen-VL-Plus和DeepSeek-Chat模型
- ✅ 实现Redis+MySQL双重缓存
- ✅ 提供完整的API接口和错误处理
- ✅ 支持多题目批量处理

---

## 🛠️ 高级配置

### 环境变量配置
如果您需要自行部署服务，可以通过以下环境变量进行配置：

```bash
# 服务器配置
export SERVER_PORT=8080
export GIN_MODE=release

# 数据库配置
export MYSQL_HOST=your-mysql-host
export MYSQL_PORT=3306
export MYSQL_USERNAME=your-username
export MYSQL_PASSWORD=your-password
export MYSQL_DATABASE=solve_api_go

# Redis配置
export REDIS_HOST=your-redis-host
export REDIS_PORT=6379
export REDIS_USERNAME=your-redis-username
export REDIS_PASSWORD=your-redis-password

# AI模型配置
export QWEN_KEY=your-qwen-api-key
export DEEPSEEK_KEY=your-deepseek-api-key
```

### 服务部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd Go_api_solve

# 2. 安装依赖
go mod tidy

# 3. 编译服务
go build -o bin/server cmd/server/main.go

# 4. 运行服务
./bin/server
```

---

## 📊 性能指标

### 响应时间参考
- **缓存命中**: 100-500ms
- **数据库命中**: 1-3秒
- **AI模型调用**: 5-30秒
- **复杂图片处理**: 30-60秒

### 系统容量
- **并发处理**: 支持100+并发请求
- **缓存容量**: Redis缓存24小时过期
- **数据存储**: MySQL支持千万级数据存储

---

## 🧪 测试工具

### 1. 健康检查
```bash
curl http://localhost:8080/api/v1/health
```

### 2. 批量测试脚本
项目提供了多种测试工具：

**Python测试脚本**
```bash
cd scripts
python api_test.py
```

**Shell测试脚本**
```bash
cd scripts
chmod +x api_test.sh
./api_test.sh
```

**Go测试程序**
```bash
cd scripts
go run api_tester.go
```

### 3. 测试图片资源
服务提供了测试图片资源，URL格式：
```
http://solve.igmdns.com/img/{01-200}.jpg
```

示例：
- `http://solve.igmdns.com/img/01.jpg`
- `http://solve.igmdns.com/img/24.jpg`
- `http://solve.igmdns.com/img/100.jpg`

---

## 🔄 业务流程详解

### 完整处理流程
1. **图片验证** → 验证URL有效性和可访问性
2. **Qwen识别** → 使用Qwen-VL-Plus进行图片识别
3. **数据格式化** → 清洗和格式化识别结果
4. **缓存查询** → 检查Redis缓存是否存在
5. **数据库查询** → Redis未命中时查询MySQL
6. **DeepSeek分析** → 数据库未命中时调用DeepSeek-Chat
7. **数据存储** → 将结果存储到MySQL和Redis
8. **结果返回** → 返回标准化的JSON响应

### 缓存策略
- **缓存键生成**: 基于格式化后的题目数据生成哈希值
- **缓存层级**: Redis(L1) → MySQL(L2) → AI模型(L3)
- **过期时间**: Redis缓存24小时自动过期
- **缓存更新**: 新数据自动写入缓存

---

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 图片无法访问
**问题**: 返回"图片资源不存在，请重新上传"
**解决方案**:
- 检查图片URL是否正确
- 确认图片资源可公开访问
- 验证网络连接是否正常

#### 2. 图片解析失败
**问题**: 返回"图片解析异常，请重新拍摄"
**解决方案**:
- 确保图片清晰度足够
- 检查图片是否包含完整题目
- 尝试使用其他格式的图片

#### 3. 请求超时
**问题**: 请求超过60秒未响应
**解决方案**:
- 检查网络连接稳定性
- 尝试使用更小尺寸的图片
- 稍后重试请求

#### 4. 服务不可用
**问题**: 返回500错误或连接失败
**解决方案**:
- 检查服务是否正常运行
- 验证数据库和Redis连接
- 查看服务日志排查问题

---

## 📈 最佳实践

### 1. 请求优化
- **图片预处理**: 建议将图片压缩到合适大小
- **URL优化**: 使用CDN加速图片访问
- **并发控制**: 合理控制并发请求数量

### 2. 错误处理
```javascript
const processImageWithRetry = async (imageUrl, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await processImage(imageUrl);
      if (result) return result;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};
```

### 3. 缓存利用
- 相同内容的图片会命中缓存，响应更快
- 建议在客户端也实现适当的缓存机制
- 避免重复提交相同的图片请求

### 4. 监控告警
- 监控API响应时间和成功率
- 设置适当的超时和重试机制
- 记录关键业务指标和错误日志

---

## 🔐 安全最佳实践

### 1. 访问控制
- 在生产环境中实现API密钥认证
- 使用HTTPS协议保护数据传输
- 实现IP白名单或访问频率限制

### 2. 数据安全
- 避免在URL中包含敏感信息
- 对上传的图片进行安全检查
- 定期清理过期的缓存数据

### 3. 监控审计
- 记录所有API调用日志
- 监控异常访问模式
- 定期进行安全评估

---

## 📋 API版本管理

### 当前版本: v1
- **路径前缀**: `/api/v1/`
- **兼容性**: 向后兼容
- **更新策略**: 重大变更会发布新版本

### 版本升级通知
- 新版本发布前会提前通知
- 旧版本会保持一定时间的兼容性
- 建议及时升级到最新版本

---

*本文档最后更新时间: 2024年12月*
