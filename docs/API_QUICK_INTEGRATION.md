# 图片搜题API快速接入指南

## 📋 接口概述

**功能**: 上传图片URL，自动识别题目并返回答案解析  
**地址**: `POST /api/v1/process-image`  
**超时**: 60秒  
**支持题型**: 单选题、多选题、判断题

---

## 🚀 快速开始

### 请求格式
```http
POST /api/v1/process-image
Content-Type: application/json

{
  "image_url": "http://solve.igmdns.com/img/24.jpg"
}
```

### 基础响应格式
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    // 题目数组，支持1-3道题目
  ]
}
```

---

## 📝 响应示例

### 1. 单个结果 - 多选题
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "多选题",
      "question_text": "雾天跟车行驶,应如何安全驾驶?",
      "options": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物",
        "D": "按喇叭提示行车位置"
      },
      "answer": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物"
      },
      "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度，以确保有足够的反应时间。其次要提前开启雾灯和危险报警闪光灯，提高车辆的可见性。同时可以以前车尾灯作为判断安全距离的参照物。",
      "image_url": "http://solve.igmdns.com/img/24.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

### 2. 单个结果 - 单选题
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "单选题",
      "question_text": "机动车在高速公路上发生故障时，应当在故障车来车方向多少米以外设置警告标志？",
      "options": {
        "A": "50米",
        "B": "100米",
        "C": "150米",
        "D": "200米"
      },
      "answer": {
        "C": "150米"
      },
      "analysis": "根据《道路交通安全法实施条例》规定，机动车在高速公路上发生故障时，应当在故障车来车方向150米以外设置警告标志。",
      "image_url": "http://solve.igmdns.com/img/01.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

### 3. 单个结果 - 判断题
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "判断题",
      "question_text": "驾驶机动车通过积水路段时，应当快速通过以免熄火。",
      "options": {
        "Y": "正确",
        "N": "错误"
      },
      "answer": {
        "N": "错误"
      },
      "analysis": "通过积水路段应当低速缓慢通过，保持低挡位匀速行驶，避免中途停车、换挡或急转方向盘，快速通过容易造成发动机进水。",
      "image_url": "http://solve.igmdns.com/img/02.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

### 4. 多个结果 - 混合题型
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "单选题",
      "question_text": "雨天驾驶时，应该开启什么灯光？",
      "options": {
        "A": "远光灯",
        "B": "近光灯",
        "C": "雾灯",
        "D": "示廓灯"
      },
      "answer": {
        "C": "雾灯"
      },
      "analysis": "雨天能见度降低，应开启雾灯以提高车辆的可见性，确保行车安全。",
      "image_url": "http://solve.igmdns.com/img/multi.jpg",
      "user_image": "",
      "is_verified": "0"
    },
    {
      "question_type": "多选题",
      "question_text": "雨天行车需要注意哪些事项？",
      "options": {
        "A": "降低车速",
        "B": "保持安全距离",
        "C": "开启示廓灯",
        "D": "频繁变道"
      },
      "answer": {
        "A": "降低车速",
        "B": "保持安全距离",
        "C": "开启示廓灯"
      },
      "analysis": "雨天路面湿滑，制动距离增加，应当降低车速、保持安全距离，并开启示廓灯提高可见性。频繁变道会增加事故风险。",
      "image_url": "http://solve.igmdns.com/img/multi.jpg",
      "user_image": "",
      "is_verified": "0"
    },
    {
      "question_type": "判断题",
      "question_text": "雨天可以正常速度行驶。",
      "options": {
        "Y": "正确",
        "N": "错误"
      },
      "answer": {
        "N": "错误"
      },
      "analysis": "雨天路面湿滑，应当降低行驶速度，以确保行车安全。",
      "image_url": "http://solve.igmdns.com/img/multi.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

---

## 🔧 前端集成代码

### JavaScript/TypeScript
```javascript
class QuestionAPI {
  constructor(baseURL = 'http://localhost:8080') {
    this.baseURL = baseURL;
  }

  async processImage(imageUrl) {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/process-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_url: imageUrl
        })
      });

      const result = await response.json();
      
      if (result.code === 200) {
        return {
          success: true,
          questions: result.data,
          count: result.data.length
        };
      } else {
        return {
          success: false,
          error: result.message
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 使用示例
const api = new QuestionAPI();

api.processImage('http://solve.igmdns.com/img/24.jpg')
  .then(result => {
    if (result.success) {
      console.log(`识别到 ${result.count} 道题目`);
      result.questions.forEach((question, index) => {
        console.log(`第${index + 1}题: ${question.question_text}`);
        console.log(`类型: ${question.question_type}`);
        console.log(`答案:`, question.answer);
      });
    } else {
      console.error('处理失败:', result.error);
    }
  });
```

### React Hook
```javascript
import { useState } from 'react';

export const useQuestionAPI = () => {
  const [loading, setLoading] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [error, setError] = useState(null);

  const processImage = async (imageUrl) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/process-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();
      
      if (result.code === 200) {
        setQuestions(result.data);
        return result.data;
      } else {
        setError(result.message);
        return null;
      }
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { processImage, loading, questions, error };
};

// 组件中使用
function QuestionComponent() {
  const { processImage, loading, questions, error } = useQuestionAPI();

  const handleSubmit = async () => {
    const imageUrl = 'http://solve.igmdns.com/img/24.jpg';
    await processImage(imageUrl);
  };

  return (
    <div>
      <button onClick={handleSubmit} disabled={loading}>
        {loading ? '处理中...' : '识别题目'}
      </button>
      
      {error && <div className="error">{error}</div>}
      
      {questions.map((question, index) => (
        <div key={index} className="question">
          <h3>第{index + 1}题 ({question.question_type})</h3>
          <p>{question.question_text}</p>
          <div className="options">
            {Object.entries(question.options).map(([key, value]) => (
              <div key={key}>{key}: {value}</div>
            ))}
          </div>
          <div className="answer">
            答案: {Object.entries(question.answer).map(([key, value]) => 
              `${key}: ${value}`
            ).join(', ')}
          </div>
          <p className="analysis">{question.analysis}</p>
        </div>
      ))}
    </div>
  );
}
```

---

## ❌ 错误处理

### 常见错误响应
```json
// 参数错误
{
  "code": 400,
  "message": "请求参数错误: Key: 'ProcessImageRequest.ImageURL' Error:Field validation for 'ImageURL' failed on the 'required' tag"
}

// 图片不存在
{
  "code": 400,
  "message": "图片资源不存在，请重新上传"
}

// 图片解析失败
{
  "code": 400,
  "message": "图片解析异常，请重新拍摄"
}

// 服务器错误
{
  "code": 500,
  "message": "服务器内部错误: 具体错误信息"
}
```

### 错误处理建议
```javascript
const handleError = (error) => {
  switch (error) {
    case '图片资源不存在，请重新上传':
      return '图片链接无效，请检查图片地址';
    case '图片解析异常，请重新拍摄':
      return '图片不清晰或不包含题目，请重新上传';
    default:
      return '处理失败，请稍后重试';
  }
};
```

---

## 📊 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| question_type | string | 题目类型：单选题/多选题/判断题 |
| question_text | string | 题目内容 |
| options | object | 选项内容，键值对格式 |
| answer | object | 正确答案，键值对格式 |
| analysis | string | 题目解析说明 |
| image_url | string | 原始图片URL |
| user_image | string | 图片文件名（空值，由管理员后期添加） |
| is_verified | string | 是否已验证：0-未验证，1-已验证 |

---

## 🔍 测试地址

- **健康检查**: `GET /api/v1/health`
- **测试图片**: `http://solve.igmdns.com/img/01.jpg` ~ `http://solve.igmdns.com/img/200.jpg`

---

**文档版本**: v1.0  
**最后更新**: 2024年12月
